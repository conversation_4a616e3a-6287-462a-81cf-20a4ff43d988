#ifndef PROPERTY_SYSTEM_CORE_CONCEPTS_H
#define PROPERTY_SYSTEM_CORE_CONCEPTS_H

#include <concepts>
#include <type_traits>
#include <utility>
#include <iterator>
#include <limits>

/**
 * @file PropertySystem/Core/Concepts.h
 * @brief Modern C++20 concepts for property system type checking
 * 
 * This file provides C++20 concepts that replace the custom TypeTraits
 * implementation, offering better compile-time error messages and
 * standardized type checking.
 */

namespace PropertySystem::Core {

// ==================================================================================
// Basic Type Concepts
// ==================================================================================

/**
 * @brief Concept to check if a type has equality comparison operator
 * @tparam T Type to check
 */
template<typename T>
concept EqualityComparable = requires(const T& a, const T& b) {
    { a == b } -> std::convertible_to<bool>;
};

/**
 * @brief Concept to check if a type has less-than comparison operator
 * @tparam T Type to check
 */
template<typename T>
concept LessThanComparable = requires(const T& a, const T& b) {
    { a < b } -> std::convertible_to<bool>;
};

/**
 * @brief Concept to check if a type is dereferenceable (has operator->)
 * @tparam T Type to check
 */
template<typename T>
concept Dereferenceable = requires(T& t) {
    { t.operator->() };
};

/**
 * @brief Concept to check if two types can be arithmetically promoted together
 * @tparam T First type
 * @tparam U Second type
 */
template<typename T, typename U>
concept ArithmeticPromotable = requires(T t, U u) {
    { t + u };
    { t - u };
    { t * u };
    { t / u };
};

// ==================================================================================
// Property System Specific Concepts
// ==================================================================================

/**
 * @brief Core concept defining what types can be used as property values
 * @tparam T Type to check
 */
template<typename T>
concept PropertyType = std::copyable<T> || std::movable<T>;

/**
 * @brief Concept for types that use reference semantics in properties
 * @tparam T Type to check
 */
template<typename T>
concept UseReferenceSemantics = std::is_reference_v<T> || 
                                std::is_pointer_v<T> ||
                                (sizeof(T) > sizeof(void*) * 2);

/**
 * @brief Concept for callable objects that can be used in property bindings
 * @tparam F Callable type
 * @tparam R Expected return type
 */
template<typename F, typename R>
concept PropertyBindingCallable = std::invocable<F> && 
                                 std::convertible_to<std::invoke_result_t<F>, R>;

// ==================================================================================
// Type Trait Helpers
// ==================================================================================

/**
 * @brief Helper to determine parameter type for properties
 * @tparam T Property value type
 */
template<typename T>
struct parameter_type {
    using type = std::conditional_t<UseReferenceSemantics<T>, const T&, T>;
};

template<typename T>
using parameter_type_t = typename parameter_type<T>::type;

/**
 * @brief Helper to determine rvalue reference type for properties
 * @tparam T Property value type
 */
template<typename T>
struct rvalue_ref {
    using type = std::conditional_t<UseReferenceSemantics<T>, T, T&&>;
};

template<typename T>
using rvalue_ref_t = typename rvalue_ref<T>::type;

/**
 * @brief Helper to determine arrow operator result type
 * @tparam T Property value type
 */
template<typename T>
struct arrow_operator_result {
    using type = std::conditional_t<std::is_pointer_v<T>, T, const T*>;
};

template<typename T>
using arrow_operator_result_t = typename arrow_operator_result<T>::type;

} // namespace PropertySystem::Core

// ==================================================================================
// 导出主要概念到全局命名空间
// ==================================================================================

// 导出主要概念到全局命名空间
using PropertySystem::Core::PropertyType;
using PropertySystem::Core::EqualityComparable;
using PropertySystem::Core::LessThanComparable;
using PropertySystem::Core::Dereferenceable;
using PropertySystem::Core::ArithmeticPromotable;

#endif // PROPERTY_SYSTEM_CORE_CONCEPTS_H
