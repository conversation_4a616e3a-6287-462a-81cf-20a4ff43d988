cmake_minimum_required(VERSION 3.20)
project(PropertySystem LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(MODULE property)
set(MODULE_SRC
    PropertySystem/PropertySystem.h
    PropertySystem/Core/PropertyTypes.h
    PropertySystem/Core/PropertyData.h
    PropertySystem/Core/Property.h
    PropertySystem/Core/Property.cpp
    PropertySystem/Core/PropertyBindingData.h
    PropertySystem/Core/PropertyBindingPrivate.h
    PropertySystem/Core/BindingEvaluationState.h
    PropertySystem/Core/PropertyBindingError.h
    PropertySystem/Core/UntypedPropertyBinding.h
    PropertySystem/Core/PropertyUpdateGroup.h
    PropertySystem/Core/PropertyUpdateGroup.cpp
    PropertySystem/Core/PropertySystemConfig.cpp
    PropertySystem/Core/Concepts.h
    PropertySystem/Core/SharedPtr.h
    PropertySystem/Binding/BindingLocation.h
    PropertySystem/Binding/BindingVTable.h
    PropertySystem/Binding/PropertyBinding.h
    PropertySystem/Observer/PropertyObserver.h
    PropertySystem/Utility/UpdateGroup.h
    PropertySystem/Utility/Container.h
)

add_library(${MODULE})

target_sources(${MODULE} PRIVATE ${MODULE_SRC})

target_include_directories(${MODULE} PUBLIC
    ${PROJECT_BINARY_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/PropertySystem
)

# PropertySystem is now self-contained and doesn't depend on global
# target_link_libraries(${MODULE} PUBLIC global)

# Add test executables
add_executable(test_compile test_compile.cpp)
target_link_libraries(test_compile PRIVATE ${MODULE})

add_executable(test_property_system test_property_system.cpp)
target_link_libraries(test_property_system PRIVATE ${MODULE})

add_executable(test_core_api test_core_api.cpp)
target_link_libraries(test_core_api PRIVATE ${MODULE})

add_executable(simple_test simple_test.cpp)
target_link_libraries(simple_test PRIVATE ${MODULE})
