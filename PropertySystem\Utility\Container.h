#ifndef PROPERTY_SYSTEM_UTILITY_CONTAINER_H
#define PROPERTY_SYSTEM_UTILITY_CONTAINER_H

#include <vector>
#include <array>
#include <memory>
#include <algorithm>
#include <iterator>
#include <type_traits>
#include <initializer_list>
#include "../Core/Concepts.h"

/**
 * @file PropertySystem/Utility/Container.h
 * @brief Modern C++20 container implementation using std::vector with small object optimization
 * 
 * This file provides a modern replacement for the custom VarLengthArray
 * implementation, using std::vector as the underlying mechanism while
 * maintaining small object optimization and API compatibility.
 */

namespace PropertySystem::Utility {

// ==================================================================================
// Small Vector Base Class
// ==================================================================================

/**
 * @brief Base class for SmallVector to reduce template instantiation
 */
class SmallVectorBase {
protected:
    void* beginPtr = nullptr;
    std::size_t size_ = 0;
    std::size_t capacity_ = 0;
    
    SmallVectorBase() = default;
    ~SmallVectorBase() = default;
    
    bool isSmall() const noexcept {
        return capacity_ <= getInlineCapacity();
    }
    
    virtual std::size_t getInlineCapacity() const noexcept = 0;
    virtual void* getInlineStorage() noexcept = 0;
    virtual const void* getInlineStorage() const noexcept = 0;
};

// ==================================================================================
// Small Vector Implementation
// ==================================================================================

/**
 * @brief Modern replacement for VarLengthArray with small object optimization
 * @tparam T Element type
 * @tparam InlineCapacity Number of elements to store inline before using heap
 * 
 * This class provides API compatibility with VarLengthArray while using
 * std::vector internally for better standard library integration.
 */
template<typename T, std::size_t InlineCapacity = 256>
requires PropertySystem::Core::PropertyType<T>
class SmallVector : public SmallVectorBase {
public:
    using value_type = T;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;
    using reference = T&;
    using const_reference = const T&;
    using pointer = T*;
    using const_pointer = const T*;
    using iterator = T*;
    using const_iterator = const T*;
    using reverse_iterator = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;

private:
    // Inline storage for small object optimization
    alignas(T) char inlineStorage_[sizeof(T) * InlineCapacity];
    
    // External storage for larger arrays
    std::vector<T> externalStorage_;

public:
    // ==================================================================================
    // Constructors and Destructor
    // ==================================================================================
    
    /**
     * @brief Default constructor
     */
    SmallVector() noexcept {
        beginPtr = getInlineStorage();
        capacity_ = InlineCapacity;
    }
    
    /**
     * @brief Constructor with size
     * @param count Number of elements to create
     */
    explicit SmallVector(size_type count) : SmallVector() {
        resize(count);
    }
    
    /**
     * @brief Constructor with size and value
     * @param count Number of elements to create
     * @param value Value to initialize elements with
     */
    SmallVector(size_type count, const T& value) : SmallVector() {
        resize(count, value);
    }
    
    /**
     * @brief Constructor from initializer list
     * @param init Initializer list
     */
    SmallVector(std::initializer_list<T> init) : SmallVector() {
        assign(init.begin(), init.end());
    }
    
    /**
     * @brief Constructor from iterator range
     * @tparam InputIt Iterator type
     * @param first Beginning of range
     * @param last End of range
     */
    template<typename InputIt>
    SmallVector(InputIt first, InputIt last) : SmallVector() {
        assign(first, last);
    }
    
    /**
     * @brief Copy constructor
     * @param other Other SmallVector to copy
     */
    SmallVector(const SmallVector& other) : SmallVector() {
        assign(other.begin(), other.end());
    }
    
    /**
     * @brief Move constructor
     * @param other Other SmallVector to move from
     */
    SmallVector(SmallVector&& other) noexcept : SmallVector() {
        if (other.isSmall()) {
            // Move elements from inline storage
            assign(std::make_move_iterator(other.begin()),
                   std::make_move_iterator(other.end()));
        } else {
            // Move the external storage
            externalStorage_ = std::move(other.externalStorage_);
            beginPtr = externalStorage_.data();
            size_ = other.size_;
            capacity_ = externalStorage_.capacity();
            
            // Reset other to inline storage
            other.beginPtr = other.getInlineStorage();
            other.size_ = 0;
            other.capacity_ = InlineCapacity;
        }
    }
    
    /**
     * @brief Destructor
     */
    ~SmallVector() {
        clear();
    }

    // ==================================================================================
    // Assignment Operators
    // ==================================================================================
    
    /**
     * @brief Copy assignment operator
     * @param other Other SmallVector to copy
     * @return Reference to this
     */
    SmallVector& operator=(const SmallVector& other) {
        if (this != &other) {
            assign(other.begin(), other.end());
        }
        return *this;
    }
    
    /**
     * @brief Move assignment operator
     * @param other Other SmallVector to move from
     * @return Reference to this
     */
    SmallVector& operator=(SmallVector&& other) noexcept {
        if (this != &other) {
            clear();
            
            if (other.isSmall()) {
                // Move elements from inline storage
                assign(std::make_move_iterator(other.begin()),
                       std::make_move_iterator(other.end()));
            } else {
                // Move the external storage
                externalStorage_ = std::move(other.externalStorage_);
                beginPtr = externalStorage_.data();
                size_ = other.size_;
                capacity_ = externalStorage_.capacity();
                
                // Reset other to inline storage
                other.beginPtr = other.getInlineStorage();
                other.size_ = 0;
                other.capacity_ = InlineCapacity;
            }
        }
        return *this;
    }
    
    /**
     * @brief Assignment from initializer list
     * @param init Initializer list
     * @return Reference to this
     */
    SmallVector& operator=(std::initializer_list<T> init) {
        assign(init.begin(), init.end());
        return *this;
    }

    // ==================================================================================
    // Element Access
    // ==================================================================================
    
    /**
     * @brief Access element at index (with bounds checking)
     * @param pos Index
     * @return Reference to element
     */
    reference at(size_type pos) {
        if (pos >= size_) {
            throw std::out_of_range("SmallVector::at");
        }
        return static_cast<pointer>(beginPtr)[pos];
    }
    
    /**
     * @brief Access element at index (with bounds checking)
     * @param pos Index
     * @return Const reference to element
     */
    const_reference at(size_type pos) const {
        if (pos >= size_) {
            throw std::out_of_range("SmallVector::at");
        }
        return static_cast<const_pointer>(beginPtr)[pos];
    }
    
    /**
     * @brief Access element at index (no bounds checking)
     * @param pos Index
     * @return Reference to element
     */
    reference operator[](size_type pos) noexcept {
        return static_cast<pointer>(beginPtr)[pos];
    }
    
    /**
     * @brief Access element at index (no bounds checking)
     * @param pos Index
     * @return Const reference to element
     */
    const_reference operator[](size_type pos) const noexcept {
        return static_cast<const_pointer>(beginPtr)[pos];
    }
    
    /**
     * @brief Access first element
     * @return Reference to first element
     */
    reference front() noexcept {
        return static_cast<pointer>(beginPtr)[0];
    }
    
    /**
     * @brief Access first element
     * @return Const reference to first element
     */
    const_reference front() const noexcept {
        return static_cast<const_pointer>(beginPtr)[0];
    }
    
    /**
     * @brief Access last element
     * @return Reference to last element
     */
    reference back() noexcept {
        return static_cast<pointer>(beginPtr)[size_ - 1];
    }
    
    /**
     * @brief Access last element
     * @return Const reference to last element
     */
    const_reference back() const noexcept {
        return static_cast<const_pointer>(beginPtr)[size_ - 1];
    }
    
    /**
     * @brief Get pointer to underlying data
     * @return Pointer to data
     */
    pointer data() noexcept {
        return static_cast<pointer>(beginPtr);
    }
    
    /**
     * @brief Get pointer to underlying data
     * @return Const pointer to data
     */
    const_pointer data() const noexcept {
        return static_cast<const_pointer>(beginPtr);
    }

    // ==================================================================================
    // Iterators
    // ==================================================================================
    
    iterator begin() noexcept { return data(); }
    const_iterator begin() const noexcept { return data(); }
    const_iterator cbegin() const noexcept { return data(); }
    
    iterator end() noexcept { return data() + size_; }
    const_iterator end() const noexcept { return data() + size_; }
    const_iterator cend() const noexcept { return data() + size_; }
    
    reverse_iterator rbegin() noexcept { return reverse_iterator(end()); }
    const_reverse_iterator rbegin() const noexcept { return const_reverse_iterator(end()); }
    const_reverse_iterator crbegin() const noexcept { return const_reverse_iterator(end()); }
    
    reverse_iterator rend() noexcept { return reverse_iterator(begin()); }
    const_reverse_iterator rend() const noexcept { return const_reverse_iterator(begin()); }
    const_reverse_iterator crend() const noexcept { return const_reverse_iterator(begin()); }

    // ==================================================================================
    // Capacity
    // ==================================================================================
    
    bool empty() const noexcept { return size_ == 0; }
    size_type size() const noexcept { return size_; }
    size_type max_size() const noexcept { return std::numeric_limits<size_type>::max(); }
    size_type capacity() const noexcept { return capacity_; }
    
    void reserve(size_type new_cap) {
        if (new_cap > capacity_) {
            grow_to_capacity(new_cap);
        }
    }
    
    void shrink_to_fit() {
        if (!isSmall() && size_ <= InlineCapacity) {
            // Move back to inline storage
            std::vector<T> temp = std::move(externalStorage_);
            beginPtr = getInlineStorage();
            capacity_ = InlineCapacity;
            
            // Move elements back
            std::uninitialized_move(temp.begin(), temp.begin() + size_, data());
        }
    }

protected:
    std::size_t getInlineCapacity() const noexcept override {
        return InlineCapacity;
    }
    
    void* getInlineStorage() noexcept override {
        return inlineStorage_;
    }
    
    const void* getInlineStorage() const noexcept override {
        return inlineStorage_;
    }

private:
    void grow_to_capacity(size_type new_cap) {
        if (isSmall()) {
            // Transition from inline to external storage
            externalStorage_.reserve(new_cap);
            externalStorage_.resize(size_);
            
            // Move elements from inline to external storage
            std::uninitialized_move(data(), data() + size_, externalStorage_.data());
            
            // Destroy elements in inline storage
            std::destroy(data(), data() + size_);
            
            // Update pointers
            beginPtr = externalStorage_.data();
            capacity_ = externalStorage_.capacity();
        } else {
            // Already using external storage, just reserve more
            externalStorage_.reserve(new_cap);
            beginPtr = externalStorage_.data();
            capacity_ = externalStorage_.capacity();
        }
    }
    
    template<typename InputIt>
    void assign(InputIt first, InputIt last) {
        clear();
        
        if constexpr (std::is_same_v<typename std::iterator_traits<InputIt>::iterator_category,
                                     std::random_access_iterator_tag>) {
            size_type count = std::distance(first, last);
            reserve(count);
        }
        
        for (auto it = first; it != last; ++it) {
            push_back(*it);
        }
    }
    
    void clear() {
        if (isSmall()) {
            std::destroy(data(), data() + size_);
        } else {
            externalStorage_.clear();
            beginPtr = getInlineStorage();
            capacity_ = InlineCapacity;
        }
        size_ = 0;
    }
    
    void resize(size_type count) {
        if (count > size_) {
            reserve(count);
            std::uninitialized_default_construct(data() + size_, data() + count);
        } else if (count < size_) {
            std::destroy(data() + count, data() + size_);
        }
        size_ = count;
    }
    
    void resize(size_type count, const T& value) {
        if (count > size_) {
            reserve(count);
            std::uninitialized_fill(data() + size_, data() + count, value);
        } else if (count < size_) {
            std::destroy(data() + count, data() + size_);
        }
        size_ = count;
    }
    
    void push_back(const T& value) {
        if (size_ >= capacity_) {
            grow_to_capacity(capacity_ * 2);
        }
        
        if (isSmall()) {
            new (data() + size_) T(value);
        } else {
            externalStorage_.push_back(value);
            beginPtr = externalStorage_.data();
        }
        ++size_;
    }
    
    void push_back(T&& value) {
        if (size_ >= capacity_) {
            grow_to_capacity(capacity_ * 2);
        }
        
        if (isSmall()) {
            new (data() + size_) T(std::move(value));
        } else {
            externalStorage_.push_back(std::move(value));
            beginPtr = externalStorage_.data();
        }
        ++size_;
    }
};

} // namespace PropertySystem::Utility

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Utility::SmallVector;
using PropertySystem::Utility::SmallVectorBase;

#endif // PROPERTY_SYSTEM_UTILITY_CONTAINER_H
