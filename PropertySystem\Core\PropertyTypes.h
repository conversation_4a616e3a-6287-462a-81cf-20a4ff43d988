/**
 * @file Core/PropertyTypes.h
 * @brief 属性系统的基础类型定义和concepts
 * 
 * 这个文件定义了Property系统的核心类型、concepts和前向声明，
 * 为整个系统提供类型安全的基础。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_TYPES_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_TYPES_H

#include "Concepts.h"
#include <type_traits>
#include <functional>

namespace PropertySystem::Core {

// ==================================================================================
// 前向声明
// ==================================================================================

class UntypedPropertyData;
template<typename T> class PropertyData;
template<typename T> class Property;

namespace Internal {
    class PropertyBindingData;
    class PropertyBindingPrivate;
    struct PropertyObserverPrivate;
    struct PropertyObserverPointer;
    struct BindingEvaluationState;
    struct PropertyDelayedNotifications;
    class PropertyBindingError;
}

// ==================================================================================
// 核心类型概念
// ==================================================================================

/**
 * @brief 属性类型概念
 * @tparam T 要检查的类型
 * 
 * 定义了可以用作属性值的类型要求。
 */
template<typename T>
concept PropertyType = PropertySystem::Core::PropertyType<T>;

/**
 * @brief 可比较类型概念
 * @tparam T 要检查的类型
 *
 * 定义了支持相等比较的类型要求。
 */
template<typename T>
concept EqualityComparable = PropertySystem::Core::EqualityComparable<T>;

/**
 * @brief 属性绑定可调用对象概念
 * @tparam F 可调用对象类型
 * @tparam R 期望的返回类型
 */
template<typename F, typename R>
concept PropertyBindingCallable = PropertySystem::Core::PropertyBindingCallable<F, R>;

// ==================================================================================
// 类型特征
// ==================================================================================

/**
 * @brief 获取属性类型的参数类型
 * @tparam T 属性值类型
 */
template<typename T>
using ParameterType = typename PropertySystem::Core::parameter_type_t<T>;

/**
 * @brief 获取属性类型的右值引用类型
 * @tparam T 属性值类型
 */
template<typename T>
using RValueRef = typename PropertySystem::Core::rvalue_ref_t<T>;

/**
 * @brief 检查类型是否使用引用语义
 * @tparam T 要检查的类型
 */
template<typename T>
constexpr bool UseReferenceSemantics = PropertySystem::Core::UseReferenceSemantics<T>;

// ==================================================================================
// 函数类型定义
// ==================================================================================

/**
 * @brief 属性观察者回调函数类型
 */
using PropertyObserverCallback = void (*)(UntypedPropertyData *);

/**
 * @brief 属性绑定包装器函数类型
 */
using PropertyBindingWrapper = bool(*)(UntypedPropertyData *dataPtr, void *functor);

/**
 * @brief 属性更新通知函数类型
 */
template<typename T>
using PropertyUpdateNotifier = void(*)(const T& oldValue, const T& newValue);

// ==================================================================================
// 枚举类型
// ==================================================================================

/**
 * @brief 属性绑定错误类型
 */
enum class PropertyBindingErrorType {
    NoError,           ///< 无错误
    BindingLoop,       ///< 绑定循环
    EvaluationError,   ///< 求值错误
    UnknownError       ///< 未知错误
};

/**
 * @brief 属性观察者状态
 */
enum class PropertyObserverState {
    Inactive,    ///< 非活动状态
    Active,      ///< 活动状态
    Evaluating   ///< 求值中
};

// ==================================================================================
// 工具类型
// ==================================================================================

/**
 * @brief 属性值包装器
 * @tparam T 属性值类型
 * 
 * 用于在某些情况下包装属性值，提供额外的元信息。
 */
template<typename T>
struct PropertyValueWrapper {
    T value;
    bool hasValue = true;
    
    PropertyValueWrapper() = default;
    PropertyValueWrapper(const T& v) : value(v) {}
    PropertyValueWrapper(T&& v) : value(std::move(v)) {}
    
    operator const T&() const { return value; }
    operator T&() { return value; }
};

/**
 * @brief 属性变更信息
 * @tparam T 属性值类型
 */
template<typename T>
struct PropertyChangeInfo {
    T oldValue;
    T newValue;
    bool fromBinding = false;
    
    PropertyChangeInfo(const T& old, const T& new_val, bool binding = false)
        : oldValue(old), newValue(new_val), fromBinding(binding) {}
};

// ==================================================================================
// 类型检查工具
// ==================================================================================

/**
 * @brief 检查类型是否为Property类型
 * @tparam T 要检查的类型
 */
template<typename T>
struct IsProperty : std::false_type {};

template<typename T>
struct IsProperty<Property<T>> : std::true_type {};

template<typename T>
constexpr bool IsPropertyV = IsProperty<T>::value;

/**
 * @brief 检查类型是否为PropertyData类型
 * @tparam T 要检查的类型
 */
template<typename T>
struct IsPropertyData : std::false_type {};

template<typename T>
struct IsPropertyData<PropertyData<T>> : std::true_type {};

template<typename T>
constexpr bool IsPropertyDataV = IsPropertyData<T>::value;

/**
 * @brief 获取Property或PropertyData的值类型
 * @tparam T Property或PropertyData类型
 */
template<typename T>
struct PropertyValueType {};

template<typename T>
struct PropertyValueType<Property<T>> {
    using type = T;
};

template<typename T>
struct PropertyValueType<PropertyData<T>> {
    using type = T;
};

template<typename T>
using PropertyValueTypeT = typename PropertyValueType<T>::type;

// ==================================================================================
// 便利别名
// ==================================================================================

/**
 * @brief 属性绑定函数类型
 * @tparam T 属性值类型
 */
template<typename T>
using PropertyBindingFunction = std::function<T()>;

/**
 * @brief 属性变更处理器类型
 * @tparam T 属性值类型
 */
template<typename T>
using PropertyChangeHandler = std::function<void(const PropertyChangeInfo<T>&)>;

/**
 * @brief 属性验证器类型
 * @tparam T 属性值类型
 */
template<typename T>
using PropertyValidator = std::function<bool(const T&)>;

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间便利别名（向后兼容）
// ==================================================================================

// 核心类型
using PropertySystem::Core::UntypedPropertyData;
using PropertySystem::Core::PropertyData;
using PropertySystem::Core::Property;

// 类型特征
using PropertySystem::Core::PropertyType;
using PropertySystem::Core::EqualityComparable;
using PropertySystem::Core::PropertyBindingCallable;

// 函数类型
using PropertySystem::Core::PropertyObserverCallback;
using PropertySystem::Core::PropertyBindingWrapper;

// 枚举类型
using PropertySystem::Core::PropertyBindingErrorType;
using PropertySystem::Core::PropertyObserverState;

// 工具类型
using PropertySystem::Core::PropertyValueWrapper;
using PropertySystem::Core::PropertyChangeInfo;

// 类型检查
using PropertySystem::Core::IsPropertyV;
using PropertySystem::Core::IsPropertyDataV;
using PropertySystem::Core::PropertyValueTypeT;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_TYPES_H
