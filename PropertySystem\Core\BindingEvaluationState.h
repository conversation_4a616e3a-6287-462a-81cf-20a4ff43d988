/**
 * @file Core/BindingEvaluationState.h
 * @brief 绑定评估状态管理
 * 
 * 这个文件实现了Property系统的绑定评估状态管理，
 * 负责依赖追踪、循环检测和评估上下文管理。
 */

#ifndef PROPERTY_SYSTEM_CORE_BINDING_EVALUATION_STATE_H
#define PROPERTY_SYSTEM_CORE_BINDING_EVALUATION_STATE_H

#include "PropertyTypes.h"
#include "../Utility/Container.h"
#include <vector>
#include <memory>
#include <thread>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyBindingPrivate;
class PropertyBindingData;
class UntypedPropertyData;

// ==================================================================================
// 绑定评估状态类
// ==================================================================================

/**
 * @brief 绑定评估状态类
 * 
 * 这个类管理绑定评估过程中的状态信息，包括：
 * - 当前正在评估的绑定
 * - 已捕获的依赖属性列表
 * - 评估状态栈管理
 * - 循环依赖检测
 */
struct BindingEvaluationState {
    // ==================================================================================
    // 构造和析构
    // ==================================================================================
    
    /**
     * @brief 构造函数
     * @param binding 正在评估的绑定
     * @param status 绑定状态指针
     */
    BindingEvaluationState(PropertyBindingPrivate *binding, BindingStatus *status);
    
    /**
     * @brief 析构函数
     * 
     * 自动恢复之前的评估状态
     */
    ~BindingEvaluationState() {
        if (current_state_) {
            *current_state_ = previous_state_;
        }
    }

    // ==================================================================================
    // 成员变量
    // ==================================================================================
    
    PropertyBindingPrivate *binding;                           ///< 当前评估的绑定
    BindingEvaluationState *previous_state_ = nullptr;         ///< 之前的评估状态
    BindingEvaluationState **current_state_ = nullptr;         ///< 当前状态指针的指针
    
    // 使用小向量优化依赖存储
    Utility::SmallVector<const PropertyBindingData*, 8> already_captured_properties_;

    // ==================================================================================
    // 依赖管理方法
    // ==================================================================================
    
    /**
     * @brief 添加已捕获的属性
     * @param property 属性绑定数据指针
     * @return 如果成功添加则返回true，如果已存在则返回false
     */
    bool addCapturedProperty(const PropertyBindingData *property);
    
    /**
     * @brief 检查属性是否已被捕获
     * @param property 属性绑定数据指针
     * @return 如果已被捕获则返回true
     */
    bool isPropertyCaptured(const PropertyBindingData *property) const;
    
    /**
     * @brief 清除所有已捕获的属性
     */
    void clearCapturedProperties();
    
    /**
     * @brief 获取已捕获属性的数量
     * @return 已捕获属性的数量
     */
    size_t capturedPropertyCount() const {
        return already_captured_properties_.size();
    }

    // ==================================================================================
    // 循环检测
    // ==================================================================================
    
    /**
     * @brief 检查是否存在循环依赖
     * @param property 要检查的属性
     * @return 如果存在循环依赖则返回true
     */
    bool hasCircularDependency(const PropertyBindingData *property) const;
    
    /**
     * @brief 检查绑定链中是否存在循环
     * @param binding 要检查的绑定
     * @return 如果存在循环则返回true
     */
    bool hasCircularBindingDependency(PropertyBindingPrivate *binding) const;

private:
    // 禁用拷贝和移动
    BindingEvaluationState(const BindingEvaluationState&) = delete;
    BindingEvaluationState(BindingEvaluationState&&) = delete;
    BindingEvaluationState& operator=(const BindingEvaluationState&) = delete;
    BindingEvaluationState& operator=(BindingEvaluationState&&) = delete;
};

// ==================================================================================
// 绑定状态管理
// ==================================================================================

/**
 * @brief 绑定状态结构
 * 
 * 管理全局的绑定评估状态和线程相关信息。
 */
struct BindingStatus {
    BindingEvaluationState *currently_evaluating_binding = nullptr;  ///< 当前评估的绑定
    std::thread::id thread_id;                                       ///< 线程ID
    void *group_update_data = nullptr;                               ///< 组更新数据
    
    /**
     * @brief 构造函数
     */
    BindingStatus() : thread_id(std::this_thread::get_id()) {}
    
    /**
     * @brief 检查是否在正确的线程中
     * @return 如果在正确的线程中则返回true
     */
    bool isInCorrectThread() const {
        return thread_id == std::this_thread::get_id();
    }
    
    /**
     * @brief 重新初始化线程ID（线程移动后调用）
     */
    void reinitAfterThreadMove() {
        thread_id = std::this_thread::get_id();
    }
};

// ==================================================================================
// 全局状态管理函数
// ==================================================================================

/**
 * @brief 获取当前线程的绑定状态
 * @return 绑定状态指针
 */
BindingStatus* getBindingStatus();

/**
 * @brief 初始化绑定状态线程ID
 */
void initBindingStatusThreadId();

/**
 * @brief 检查是否有绑定正在评估
 * @return 如果有绑定正在评估则返回true
 */
bool isAnyBindingEvaluating();

/**
 * @brief 暂停当前绑定状态
 * @return 被暂停的绑定评估状态
 */
BindingEvaluationState* suspendCurrentBindingStatus();

/**
 * @brief 恢复绑定状态
 * @param status 要恢复的绑定评估状态
 */
void restoreBindingStatus(BindingEvaluationState *status);

// ==================================================================================
// RAII辅助类
// ==================================================================================

/**
 * @brief 作用域值回滚类
 * @tparam T 值类型
 * 
 * 在作用域结束时自动恢复变量的原始值。
 */
template<typename T>
class ScopedValueRollback {
public:
    /**
     * @brief 构造函数
     * @param var 要管理的变量引用
     */
    explicit constexpr ScopedValueRollback(T &var)
        : var_ref_(var), old_value_(var) {}
    
    /**
     * @brief 构造函数（设置新值）
     * @param var 要管理的变量引用
     * @param value 新值
     */
    explicit constexpr ScopedValueRollback(T &var, T value)
        : var_ref_(var), old_value_(std::move(var)) {
        var = std::move(value);
    }
    
    /**
     * @brief 析构函数
     * 
     * 自动恢复原始值
     */
    ~ScopedValueRollback() {
        var_ref_ = std::move(old_value_);
    }
    
    /**
     * @brief 提交当前值（不再回滚）
     */
    constexpr void commit() {
        old_value_ = var_ref_;
    }

private:
    T &var_ref_;
    T old_value_;
    
    // 禁用拷贝和移动
    ScopedValueRollback(const ScopedValueRollback&) = delete;
    ScopedValueRollback(ScopedValueRollback&&) = delete;
    ScopedValueRollback& operator=(const ScopedValueRollback&) = delete;
    ScopedValueRollback& operator=(ScopedValueRollback&&) = delete;
};

// ==================================================================================
// 绑定状态访问令牌
// ==================================================================================

/**
 * @brief 绑定状态访问令牌
 * 
 * 用于控制对绑定状态的访问权限。
 */
struct BindingStatusAccessToken {
    // 空结构体，仅用作访问控制
};

/**
 * @brief 获取绑定状态（需要访问令牌）
 * @param token 访问令牌
 * @return 绑定状态指针
 */
BindingStatus* getBindingStatus(BindingStatusAccessToken token);

// ==================================================================================
// 内联实现
// ==================================================================================

inline bool BindingEvaluationState::addCapturedProperty(const PropertyBindingData *property) {
    // 检查是否已存在
    for (const auto *captured : already_captured_properties_) {
        if (captured == property) {
            return false; // 已存在
        }
    }
    
    // 添加新属性
    already_captured_properties_.push_back(property);
    return true;
}

inline bool BindingEvaluationState::isPropertyCaptured(const PropertyBindingData *property) const {
    for (const auto *captured : already_captured_properties_) {
        if (captured == property) {
            return true;
        }
    }
    return false;
}

inline void BindingEvaluationState::clearCapturedProperties() {
    already_captured_properties_.clear();
}

} // namespace PropertySystem::Core::Internal

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::Internal::BindingEvaluationState;
using PropertySystem::Core::Internal::BindingStatus;
using PropertySystem::Core::Internal::ScopedValueRollback;
using PropertySystem::Core::Internal::BindingStatusAccessToken;

#endif // PROPERTY_SYSTEM_CORE_BINDING_EVALUATION_STATE_H
