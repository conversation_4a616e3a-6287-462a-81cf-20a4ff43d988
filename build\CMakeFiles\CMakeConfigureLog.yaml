
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      2
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35211 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      LINK : fatal error LNK1104: 无法打开文件“LIBCMT.lib”
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35211 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCXXCompilerId.cpp
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/3.28.1/CompilerIdCXX/CMakeCXXCompilerId.obj
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:1182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:247 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-dyzplx"
      binary: "E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-dyzplx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-dyzplx'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_c8759/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_c8759.dir\\build.make CMakeFiles/cmTC_c8759.dir/build
        mingw32-make[1]: Entering directory 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-dyzplx'
        Building CXX object CMakeFiles/cmTC_c8759.dir/CMakeCXXCompilerABI.cpp.obj
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_cl_compile_depends --dep-file=CMakeFiles\\cmTC_c8759.dir\\CMakeCXXCompilerABI.cpp.obj.d --working-dir="E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dyzplx" --filter-prefix="娉ㄦ剰: 鍖呭惈鏂囦欢:  " -- C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_c8759.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_c8759.dir/ /FS -c "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
        CMakeCXXCompilerABI.cpp
        娉ㄦ剰: 鍖呭惈鏂囦欢:  C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCompilerABI.h
        Linking CXX executable cmTC_c8759.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_c8759.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_c8759.dir --rc=rc --mt=CMAKE_MT-NOTFOUND --manifests -- C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo @CMakeFiles\\cmTC_c8759.dir\\objects1.rsp  /out:cmTC_c8759.exe /implib:cmTC_c8759.lib /pdb:"E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dyzplx\\cmTC_c8759.pdb" /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib 
        Visual Studio Incremental Link with embedded manifests
        Create CMakeFiles\\cmTC_c8759.dir/manifest.rc
        Create empty: CMakeFiles\\cmTC_c8759.dir/embed.manifest
        RC Pass 1:
        rc /fo CMakeFiles\\cmTC_c8759.dir/manifest.res CMakeFiles\\cmTC_c8759.dir/manifest.rc
        RC Pass 1: command "rc /fo CMakeFiles\\cmTC_c8759.dir/manifest.res CMakeFiles\\cmTC_c8759.dir/manifest.rc" failed (exit code 0) with the following output:
        no such file or directorymingw32-make[1]: *** [CMakeFiles\\cmTC_c8759.dir\\build.make:100: cmTC_c8759.exe] Error -1
        mingw32-make[1]: Leaving directory 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-dyzplx'
        mingw32-make: *** [Makefile:126: cmTC_c8759/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:49 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working CXX compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe"
    directories:
      source: "E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-wdy19r"
      binary: "E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-wdy19r"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-wdy19r'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_27e5b/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_27e5b.dir\\build.make CMakeFiles/cmTC_27e5b.dir/build
        mingw32-make[1]: Entering directory 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-wdy19r'
        Building CXX object CMakeFiles/cmTC_27e5b.dir/testCXXCompiler.cxx.obj
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_cl_compile_depends --dep-file=CMakeFiles\\cmTC_27e5b.dir\\testCXXCompiler.cxx.obj.d --working-dir="E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy19r" --filter-prefix="娉ㄦ剰: 鍖呭惈鏂囦欢:  " -- C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_27e5b.dir\\testCXXCompiler.cxx.obj /FdCMakeFiles\\cmTC_27e5b.dir/ /FS -c "E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy19r\\testCXXCompiler.cxx"
        testCXXCompiler.cxx
        Linking CXX executable cmTC_27e5b.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_27e5b.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_27e5b.dir --rc=rc --mt=CMAKE_MT-NOTFOUND --manifests -- C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo @CMakeFiles\\cmTC_27e5b.dir\\objects1.rsp  /out:cmTC_27e5b.exe /implib:cmTC_27e5b.lib /pdb:"E:\\Projects\\Code\\AI Agent\\core\\props\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wdy19r\\cmTC_27e5b.pdb" /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib 
        Visual Studio Incremental Link with embedded manifests
        Create CMakeFiles\\cmTC_27e5b.dir/manifest.rc
        Create empty: CMakeFiles\\cmTC_27e5b.dir/embed.manifest
        RC Pass 1:
        rc /fo CMakeFiles\\cmTC_27e5b.dir/manifest.res CMakeFiles\\cmTC_27e5b.dir/manifest.rc
        RC Pass 1: command "rc /fo CMakeFiles\\cmTC_27e5b.dir/manifest.res CMakeFiles\\cmTC_27e5b.dir/manifest.rc" failed (exit code 0) with the following output:
        no such file or directorymingw32-make[1]: *** [CMakeFiles\\cmTC_27e5b.dir\\build.make:100: cmTC_27e5b.exe] Error -1
        mingw32-make[1]: Leaving directory 'E:/Projects/Code/AI Agent/core/props/build/CMakeFiles/CMakeScratch/TryCompile-wdy19r'
        mingw32-make: *** [Makefile:126: cmTC_27e5b/fast] Error 2
        
      exitCode: 2
...
