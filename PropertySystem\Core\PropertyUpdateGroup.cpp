/**
 * @file Core/PropertyUpdateGroup.cpp
 * @brief 属性更新组实现
 * 
 * 这个文件提供了Property系统更新组的具体实现。
 */

#include "PropertyUpdateGroup.h"
#include "BindingEvaluationState.h"
#include <atomic>
#include <memory>

namespace PropertySystem::Core {

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace Internal {

UpdateGroupState& UpdateGroupState::global() {
    static UpdateGroupState instance;
    return instance;
}

PropertyDelayedNotifications* getCurrentDelayedNotifications() {
    return UpdateGroupState::global().notifications.get();
}

void setCurrentDelayedNotifications(PropertyDelayedNotifications *notifications) {
    auto& state = UpdateGroupState::global();
    state.notifications.reset(notifications);
}

void processDelayedNotifications() {
    auto& state = UpdateGroupState::global();
    auto notifications = std::move(state.notifications);
    
    if (!notifications) {
        return;
    }
    
    // 处理所有延迟的通知
    auto* current = notifications.get();
    while (current) {
        for (size_t i = 0; i < current->used; ++i) {
            current->notify(i);
            current->cleanup(i);
        }
        current = current->next.get();
    }
}

// PropertyDelayedNotifications实现
void PropertyDelayedNotifications::addProperty(const PropertyBindingData *bindingData, 
                                              UntypedPropertyData *propertyData) {
    // 如果已经是延迟通知，则不需要重复添加
    if (bindingData->isNotificationDelayed()) {
        return;
    }
    
    // 找到有空间的页面
    auto* data = this;
    while (data->used >= capacity) {
        if (!data->next) {
            data->next = std::make_unique<PropertyDelayedNotifications>();
        }
        data = data->next.get();
    }
    
    // 添加到延迟列表
    auto& delayed = data->delayed_properties[data->used];
    delayed.d_ptr = bindingData->d();
    delayed.original_binding_data = bindingData;
    delayed.property_data = propertyData;
    ++data->used;
    
    // 标记为延迟通知（这里需要修改bindingData的内部状态）
    // 具体实现需要访问PropertyBindingData的私有成员
}

void PropertyDelayedNotifications::evaluateBindings(size_t index, 
                                                   std::vector<class PropertyObserver*> &bindingObservers, 
                                                   struct BindingEvaluationState *status) {
    if (index >= used) {
        return;
    }
    
    auto& delayed = delayed_properties[index];
    if (!delayed.original_binding_data) {
        return;
    }
    
    // 恢复原始绑定数据状态
    // 具体实现需要访问PropertyBindingData的私有成员
    
    // 评估绑定
    // 这里需要调用绑定的评估逻辑
}

void PropertyDelayedNotifications::notify(size_t index) {
    if (index >= used) {
        return;
    }
    
    auto& delayed = delayed_properties[index];
    if (!delayed.property_data) {
        return;
    }
    
    // 通知观察者
    // 具体实现需要调用观察者的通知逻辑
}

void PropertyDelayedNotifications::cleanup(size_t index) {
    if (index >= used) {
        return;
    }
    
    auto& delayed = delayed_properties[index];
    delayed.d_ptr = 0;
    delayed.original_binding_data = nullptr;
    delayed.property_data = nullptr;
}

} // namespace Internal

// ==================================================================================
// 更新组管理函数实现
// ==================================================================================

void beginPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    int oldDepth = state.depth.fetch_add(1, std::memory_order_relaxed);
    
    // 如果是第一层，创建延迟通知对象
    if (oldDepth == 0) {
        auto notifications = std::make_unique<Internal::PropertyDelayedNotifications>();
        state.notifications = std::move(notifications);
    }
}

void endPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    int newDepth = state.depth.fetch_sub(1, std::memory_order_relaxed) - 1;
    
    if (newDepth < 0) {
        // 错误：没有匹配的beginPropertyUpdateGroup调用
        state.depth.store(0, std::memory_order_relaxed);
        return;
    }
    
    // 如果是最后一层，处理所有延迟通知
    if (newDepth == 0) {
        Internal::processDelayedNotifications();
    }
}

bool isInPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    return state.depth.load(std::memory_order_relaxed) > 0;
}

int getPropertyUpdateGroupDepth() {
    auto& state = Internal::UpdateGroupState::global();
    return state.depth.load(std::memory_order_relaxed);
}

} // namespace PropertySystem::Core
